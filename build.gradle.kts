import BuildUtils.convertMarkdownToHtml
import BuildUtils.getLocalizedMarkdownContent
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion
import java.util.*
import java.time.Instant

// Define the plugins used in this project
plugins {
    id("java")                                       // Adds Java compilation support
    id("org.jetbrains.kotlin.jvm") version "2.0.0" // 使用支持 K2 的 Kotlin 版本
    id("org.jetbrains.intellij.platform") version "2.6.0"
    id("org.jetbrains.intellij.platform.migration") version "2.6.0"
}

// Configure repositories for dependency resolution
repositories {
    mavenCentral()  // Use Maven Central repository for downloading dependencies
    intellijPlatform{
        defaultRepositories()
    }
}

val luceneVersion = "9.12.1"
// Define project dependencies
// These are the external libraries that the project depends on
dependencies {
    // JetBrains annotations for better code analysis
    // Provides a set of annotations to enhance code inspections and IDE support
    implementation("org.jetbrains:annotations:20.1.0")

    implementation("org.json:json:20090211")

    // OkHttp client for efficient HTTP requests
    // A powerful HTTP client for making network requests with features like connection pooling
    implementation("com.squareup.okhttp3:okhttp:4.10.0")

    // CommonMark library for Markdown processing
    // Provides parsing and rendering capabilities for Markdown content
    implementation("org.commonmark:commonmark:0.20.0")

    // Kotlin standard library
    // Contains essential components for Kotlin development
    implementation(kotlin("stdlib", "2.0.0"))

    // Kotlin reflection library
    // Enables runtime introspection of Kotlin code
    implementation(kotlin("reflect", "2.0.0"))

    // Kotlin coroutines core library
    // Provides support for asynchronous programming with coroutines
//    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
    // 不直接引用coroutines，改为使用idea的coroutines
    // 直接引用会导致TocoInlineCompletionProvider的flow出现ClassCastException
    // ClassCastException是因为插件引用的coroutines和idea的coroutines版本不一致导致的
    compileOnly("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0")

    // Apache Lucene for BM25 search
    implementation("org.apache.lucene:lucene-core:$luceneVersion")
    implementation("org.apache.lucene:lucene-queryparser:$luceneVersion")
    implementation("org.apache.lucene:lucene-analysis-common:$luceneVersion")
    implementation("org.apache.lucene:lucene-codecs:$luceneVersion")
    implementation("org.apache.lucene:lucene-backward-codecs:$luceneVersion")

    implementation("org.lz4:lz4-java:1.8.0")
    implementation("net.lingala.zip4j:zip4j:2.11.5")

    intellijPlatform {
        create("IC", "2024.3")
        bundledPlugins(
            "com.intellij.java",
            "org.jetbrains.kotlin",
            "org.jetbrains.idea.maven"
        )
    }
}

// 多环境配置支持
val buildEnvironment = project.findProperty("environment")?.toString() ?: "internal"
println("Building for environment: $buildEnvironment")

// 加载配置的函数
fun loadEnvironmentConfig(environment: String): Properties {
    val commonProps = Properties()
    val envProps = Properties()

    // 加载通用配置
    val commonFile = file("src/main/resources/config/common.properties")
    if (commonFile.canRead()) {
        commonFile.inputStream().use { commonProps.load(it) }
    }

    // 加载环境特定配置
    val envFile = file("src/main/resources/config/${environment}.properties")
    if (envFile.canRead()) {
        envFile.inputStream().use { envProps.load(it) }
    }

    // 合并配置（环境配置覆盖通用配置）
    val mergedProps = Properties()
    mergedProps.putAll(commonProps)
    mergedProps.putAll(envProps)

    return mergedProps
}

// 加载配置
val configProps = loadEnvironmentConfig(buildEnvironment)

// 如果环境配置加载失败，fallback到原始config.properties
if (configProps.isEmpty) {
    val fallbackFile = file("src/main/resources/config.properties")
    if (fallbackFile.canRead()) {
        fallbackFile.inputStream().use { configProps.load(it) }
    } else {
        throw GradleException("Could not read any configuration files!")
    }
}

// Extract the project version and name from the properties
val projectVersion: String = configProps.getProperty("project.version")
val projectName: String = configProps.getProperty("project.name")
val projectPluginId: String = configProps.getProperty("project.plugin.id")
val projectGroupId: String = configProps.getProperty("project.group.id")
val pluginDisplayName: String = configProps.getProperty("plugin.display.name") ?: projectName
val pluginVendorUrl: String = configProps.getProperty("plugin.vendor.url") ?: "https://toco.teitui.com/"
val pluginVendorEmail: String = configProps.getProperty("plugin.vendor.email") ?: "<EMAIL>"

// Define project group and version
group = projectGroupId
version = projectVersion

intellijPlatform {
    buildSearchableOptions = false

    pluginConfiguration {
        id = configProps.getProperty("project.plugin.id") ?: projectPluginId
        name = configProps.getProperty("plugin.display.name") ?: pluginDisplayName
        version = projectVersion

        val descriptionContent = getLocalizedMarkdownContent("description", "descriptions")
        description = convertMarkdownToHtml(descriptionContent)

        val changeNotesContent = getLocalizedMarkdownContent("change-notes", "changenotes")
        changeNotes = convertMarkdownToHtml(changeNotesContent)

        vendor {
            name = "think1024"
            email = configProps.getProperty("plugin.vendor.email") ?: pluginVendorEmail
            url = configProps.getProperty("plugin.vendor.url") ?: pluginVendorUrl
        }

        ideaVersion {
            sinceBuild = "243.0"
            untilBuild = "252.*"
        }
    }

    signing {
        certificateChain = System.getenv("CERTIFICATE_CHAIN")
        privateKey = System.getenv("PRIVATE_KEY")
        password = System.getenv("PRIVATE_KEY_PASSWORD")
    }

    publishing {
        host = "https://vs-env.byteawake.com"
        token = System.getenv("PUBLISH_TOKEN")
        // The pluginVersion is based on the SemVer (https://semver.org) and supports pre-release labels, like 2.1.7-alpha.3
        // Specify pre-release label to publish the plugin in a custom Release Channel automatically. Read more:
        // https://plugins.jetbrains.com/docs/intellij/deployment.html#specifying-a-release-channel
        channels = listOf(getPublishChannel(projectVersion))
    }

    pluginVerification {
        ides {
            recommended()
        }
    }
}

// Configure the source directories
sourceSets {
    main {
        kotlin.srcDirs("src/main/kotlin")        // Kotlin source directory
        resources {
            srcDirs("src/main/resources", "bin")        // Resources directory
        }
    }
}

tasks {
    // Set the JVM compatibility versions for Java compilation
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
    }

    // Set the JVM compatibility versions for Kotlin compilation
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_17
            // 明确使用 K2 编译器
            languageVersion = KotlinVersion.KOTLIN_2_0
            apiVersion = KotlinVersion.KOTLIN_2_0
            // 禁用特定警告
            freeCompilerArgs.addAll(listOf(
                "-Xskip-prerelease-check",
            ))
        }
    }

    // 生成环境配置文件的任务
    val generateEnvironmentConfig by registering {
        description = "Generate merged config.properties for the target environment"
        group = "build"

        // 在配置阶段获取环境变量，避免在执行时访问 project
        val targetEnvironment = project.findProperty("environment")?.toString() ?: "internal"
        val projectDirectory = layout.projectDirectory.asFile
        val buildDirectory = layout.buildDirectory

        // 添加输入输出配置，让Gradle正确跟踪变化
        inputs.property("environment", targetEnvironment)
        inputs.files(
            fileTree("src/main/resources/config") {
                include("*.properties")
            }
        )
        outputs.file(buildDirectory.file("resources/main/config.properties"))
        outputs.file(buildDirectory.file("resources/main/build-environment.properties"))

        // 强制在环境变化时重新执行
        outputs.upToDateWhen { false }

        doLast {
            // 内联加载配置逻辑以避免配置缓存问题
            val commonProps = Properties()
            val envProps = Properties()

            // 加载通用配置
            val commonFile = File(projectDirectory, "src/main/resources/config/common.properties")
            if (commonFile.exists()) {
                commonFile.inputStream().use { commonProps.load(it) }
            }

            // 加载环境特定配置
            val envFile = File(projectDirectory, "src/main/resources/config/${targetEnvironment}.properties")
            if (envFile.exists()) {
                envFile.inputStream().use { envProps.load(it) }
            }

            // 合并配置（环境配置覆盖通用配置）
            val mergedProps = Properties()
            mergedProps.putAll(commonProps)
            mergedProps.putAll(envProps)

            val outputDir = buildDirectory.get().asFile.resolve("resources/main")
            outputDir.mkdirs()

            val outputFile = outputDir.resolve("config.properties")
            outputFile.writeText("# Generated configuration for environment: $targetEnvironment\n")
            outputFile.appendText("# Generated at: ${Instant.now()}\n\n")

            // 写入合并后的配置
            mergedProps.stringPropertyNames().sorted().forEach { key ->
                val value = mergedProps.getProperty(key)
                outputFile.appendText("$key=$value\n")
            }

            // 生成环境标识文件
            val buildEnvFile = outputDir.resolve("build-environment.properties")
            buildEnvFile.writeText("# Build environment information\n")
            buildEnvFile.appendText("# Generated at: ${Instant.now()}\n")
            buildEnvFile.appendText("build.environment=$targetEnvironment\n")
            buildEnvFile.appendText("build.timestamp=${System.currentTimeMillis()}\n")

            println("Generated config.properties for environment: $targetEnvironment")
        }
    }

    // 确保在处理资源前生成配置文件
    processResources {
        dependsOn(generateEnvironmentConfig)
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    }

    // Configure IDE run task
    // This sets JVM arguments for running the plugin in a development instance of IntelliJ IDEA
    runIde {
        jvmArgs = listOf("-Xmx4G")               // Set maximum heap size to 4GB
        systemProperty("kotlin.compiler.mode", "k2")
        systemProperty("idea.auto.reload.plugins", "true")
        systemProperty("idea.debug.mode", "true")
        systemProperty("ide.browser.jcef.log.level", "error")
    }

    // Configure test execution
    // This specifies to use JUnit Platform for running tests
    test {
        useJUnitPlatform()
    }



    // 配置插件构建任务
    buildPlugin {
        // 确保包含 META-INF 目录
        from("src/main/resources/META-INF") {
            into("META-INF")
        }

        val environment = project.findProperty("environment")?.toString() ?: "internal"
        archiveFileName.set("${projectName}-${environment}-${projectVersion}.zip")  // 包含环境信息的文件名
    }

    // 内部包
    val buildPluginInternal by registering(GradleBuild::class) {
        description = "Build plugin for internal environment"
        group = "build"

        // 不用每次都clean，使用gradle的增量构建
//        tasks = listOf("clean", "buildPlugin")
        tasks = listOf("buildPlugin")
        startParameter.projectProperties["environment"] = "internal"

        doLast {
            println("Built plugin for internal environment")
        }
    }

    // 外部包
    val buildPluginPublic by registering(GradleBuild::class) {
        description = "Build plugin for public environment"
        group = "build"

        // FIXME: 打外部包时，可以考虑每次先clean
//        tasks = listOf("clean", "buildPlugin")
        tasks = listOf("buildPlugin")
        startParameter.projectProperties["environment"] = "public"

        doLast {
            println("Built plugin for public environment")
        }
    }

    // 发布内部包的便捷任务
    val publishInternalPlugin by registering {
        description = "Build and publish plugin for internal environment"
        group = "publishing"

        dependsOn(buildPluginInternal)
        finalizedBy("publishPlugin")
    }
}

// Helper function to determine the publishing channel based on the version string
fun getPublishChannel(version: String): String {
    return when {
        version.contains("beta") -> "beta"
        else -> "stable"
    }
}
